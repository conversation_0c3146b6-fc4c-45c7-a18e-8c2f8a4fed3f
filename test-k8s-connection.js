#!/usr/bin/env node

// Test script to verify Kubernetes connection
const { KubernetesService } = require('../dist/services/kubernetes-service');

async function testKubernetesConnection() {
  console.log('🧪 Testing Kubernetes connection...');
  
  try {
    // Initialize the Kubernetes service
    await KubernetesService.initialize();
    console.log('✅ Kubernetes client initialized successfully');
    
    // Test creating a simple job
    const testClientId = `test-${Date.now()}`;
    console.log(`🔧 Testing ZAP Job creation for CLIENT_ID: ${testClientId}`);
    
    const jobInfo = await KubernetesService.createZapJob(testClientId);
    
    if (jobInfo) {
      console.log('✅ ZAP Job created successfully:', {
        jobName: jobInfo.jobName,
        serviceName: jobInfo.serviceName,
        host: jobInfo.host,
        port: jobInfo.port
      });
      
      // Wait a bit then clean up
      console.log('⏳ Waiting 10 seconds before cleanup...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      console.log('🧹 Cleaning up test job...');
      await KubernetesService.deleteZapJob(testClientId);
      console.log('✅ Test job cleaned up successfully');
      
    } else {
      console.error('❌ Failed to create ZAP Job');
      process.exit(1);
    }
    
    console.log('🎉 Kubernetes connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Kubernetes connection test failed:', error.message);
    console.error('💡 Make sure:');
    console.error('   - Minikube is running: minikube status');
    console.error('   - kubectl context is set: kubectl config current-context');
    console.error('   - ZAP image is available: minikube image ls | grep zap');
    process.exit(1);
  }
}

// Run the test
testKubernetesConnection();
