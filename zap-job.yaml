apiVersion: batch/v1
kind: Job
metadata:
  name: zap-scan-job # A unique name for your ZAP job
spec:
  template:
    spec:
      containers:
      - name: zap-container
        image: zaproxy/zap-stable # Your ZAP Docker image
        command: ["zap.sh"] # The primary command to execute
        args: # Arguments passed to the command
          - "-daemon"
          - "-host"
          - "0.0.0.0"
          - "-port"
          - "8083"
          - "-config"
          - "api.addrs.addr.name=.*"
          - "-config"
          - "api.addrs.addr.regex=true"
          - "-config"
          - "api.key=AgentqSuperAI"
        ports:
        - containerPort: 8083 # Expose the ZAP API port within the pod
        # You might want to add resource requests/limits here:
        # resources:
        #   requests:
        #     memory: "1Gi"
        #     cpu: "1"
        #   limits:
        #     memory: "2Gi"
        #     cpu: "2"
      restartPolicy: Never # Crucial for Jobs - ensures the pod terminates after completion
  backoffLimit: 3 # Optional: Number of times to retry a failed pod before giving up