#!/bin/bash

# Setup script for Minikube development environment
# This script configures Minikube for ZAP security testing

set -e

echo "🚀 Setting up Minikube for ZAP Security Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Minikube is installed
if ! command -v minikube &> /dev/null; then
    print_error "Minikube is not installed. Please install it first:"
    echo "  macOS: brew install minikube"
    echo "  Linux: curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64"
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed. Please install it first:"
    echo "  macOS: brew install kubectl"
    echo "  Linux: curl -LO https://dl.k8s.io/release/v1.28.0/bin/linux/amd64/kubectl"
    exit 1
fi

print_status "Checking Minikube status..."

# Start Minikube if not running
if ! minikube status &> /dev/null; then
    print_status "Starting Minikube..."
    minikube start --driver=docker --memory=4096 --cpus=2
    print_success "Minikube started successfully"
else
    print_success "Minikube is already running"
fi

# Enable required addons
print_status "Enabling Minikube addons..."
minikube addons enable metrics-server
minikube addons enable dashboard

# Set kubectl context to minikube
print_status "Setting kubectl context to minikube..."
kubectl config use-context minikube

# Create namespace if it doesn't exist
NAMESPACE="default"
print_status "Ensuring namespace '$NAMESPACE' exists..."
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Create RBAC permissions for the service
print_status "Creating RBAC permissions..."
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ServiceAccount
metadata:
  name: zap-service-account
  namespace: $NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: $NAMESPACE
  name: zap-job-manager
rules:
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["services", "pods"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: zap-job-manager-binding
  namespace: $NAMESPACE
subjects:
- kind: ServiceAccount
  name: zap-service-account
  namespace: $NAMESPACE
roleRef:
  kind: Role
  name: zap-job-manager
  apiGroup: rbac.authorization.k8s.io
EOF

print_success "RBAC permissions created"

# Pull ZAP image to Minikube
print_status "Pulling ZAP image to Minikube..."
minikube image pull zaproxy/zap-stable:latest

# Verify setup
print_status "Verifying setup..."

# Check cluster info
echo ""
print_status "Cluster Information:"
kubectl cluster-info

echo ""
print_status "Current Context:"
kubectl config current-context

echo ""
print_status "Available Nodes:"
kubectl get nodes

echo ""
print_status "Available Namespaces:"
kubectl get namespaces

echo ""
print_success "Minikube setup completed successfully!"
echo ""
print_status "Next steps:"
echo "  1. Set environment variables:"
echo "     export USE_KUBERNETES=true"
echo "     export K8S_NAMESPACE=default"
echo ""
echo "  2. Start your WebSocket service:"
echo "     npm run dev"
echo ""
echo "  3. Monitor ZAP jobs:"
echo "     kubectl get jobs -l app=zap-scan"
echo "     kubectl get pods -l app=zap-scan"
echo ""
echo "  4. View logs:"
echo "     kubectl logs -l app=zap-scan"
echo ""
echo "  5. Access Minikube dashboard (optional):"
echo "     minikube dashboard"
