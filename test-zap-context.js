/**
 * Test script to demonstrate the enhanced ZAP service with scan context information
 * This script shows how the ZAP JSON report now includes details about which URLs were scanned
 */

const axios = require('axios');

// ZAP configuration
const ZAP_HOST = 'http://localhost:8080';
const ZAP_API_KEY = 'AgentqSuperAI';

/**
 * Fetch scan context information from ZAP (similar to the enhanced ZapService)
 */
async function fetchZapScanContext() {
  const scanContext = {
    sites: [],
    urls: [],
    hosts: [],
    scanStartTime: new Date().toISOString(),
    scanEndTime: new Date().toISOString()
  };

  try {
    console.log('🔍 Fetching ZAP scan context information...\n');

    // Fetch sites that were accessed
    console.log('📍 Fetching sites...');
    const sitesResponse = await axios.get(
      `${ZAP_HOST}/JSON/core/view/sites/?apikey=${ZAP_API_KEY}`,
      { timeout: 5000 }
    );
    if (sitesResponse.data && sitesResponse.data.sites) {
      scanContext.sites = sitesResponse.data.sites;
      console.log(`   Found ${scanContext.sites.length} sites:`);
      scanContext.sites.forEach((site, index) => {
        console.log(`   ${index + 1}. ${site}`);
      });
    }
    console.log('');

    // Fetch URLs that were accessed
    console.log('🔗 Fetching URLs...');
    const urlsResponse = await axios.get(
      `${ZAP_HOST}/JSON/core/view/urls/?apikey=${ZAP_API_KEY}`,
      { timeout: 5000 }
    );
    if (urlsResponse.data && urlsResponse.data.urls) {
      scanContext.urls = urlsResponse.data.urls;
      console.log(`   Found ${scanContext.urls.length} URLs:`);
      scanContext.urls.slice(0, 10).forEach((url, index) => {
        console.log(`   ${index + 1}. ${url}`);
      });
      if (scanContext.urls.length > 10) {
        console.log(`   ... and ${scanContext.urls.length - 10} more URLs`);
      }
    }
    console.log('');

    // Fetch hosts that were accessed
    console.log('🌐 Fetching hosts...');
    const hostsResponse = await axios.get(
      `${ZAP_HOST}/JSON/core/view/hosts/?apikey=${ZAP_API_KEY}`,
      { timeout: 5000 }
    );
    if (hostsResponse.data && hostsResponse.data.hosts) {
      scanContext.hosts = hostsResponse.data.hosts;
      console.log(`   Found ${scanContext.hosts.length} hosts:`);
      scanContext.hosts.forEach((host, index) => {
        console.log(`   ${index + 1}. ${host}`);
      });
    }
    console.log('');

    // Fetch alerts for comparison
    console.log('⚠️  Fetching alerts (vulnerabilities)...');
    const alertsResponse = await axios.get(
      `${ZAP_HOST}/JSON/core/view/alerts/?apikey=${ZAP_API_KEY}`,
      { timeout: 5000 }
    );
    if (alertsResponse.data && alertsResponse.data.alerts) {
      console.log(`   Found ${alertsResponse.data.alerts.length} alerts/vulnerabilities`);
      alertsResponse.data.alerts.slice(0, 5).forEach((alert, index) => {
        console.log(`   ${index + 1}. ${alert.name} (${alert.risk} risk)`);
      });
      if (alertsResponse.data.alerts.length > 5) {
        console.log(`   ... and ${alertsResponse.data.alerts.length - 5} more alerts`);
      }
    }
    console.log('');

    // Set target URL from the first site if available
    if (scanContext.sites.length > 0) {
      scanContext.targetUrl = scanContext.sites[0];
    }

    return scanContext;

  } catch (error) {
    console.error('❌ Error fetching ZAP scan context:', error.message);
    return scanContext;
  }
}

/**
 * Main function to demonstrate the enhanced scan context
 */
async function main() {
  console.log('🚀 Testing Enhanced ZAP Service with Scan Context\n');
  console.log('This demonstrates how the ZAP JSON report now includes:');
  console.log('- Sites that were scanned');
  console.log('- URLs that were accessed');
  console.log('- Hosts that were contacted');
  console.log('- Target URL information');
  console.log('- Scan statistics\n');
  console.log('=' .repeat(60));

  try {
    // Check if ZAP is running
    const healthResponse = await axios.get(`${ZAP_HOST}/JSON/core/view/version/?apikey=${ZAP_API_KEY}`, { timeout: 3000 });
    console.log(`✅ ZAP is running (version: ${healthResponse.data.version})\n`);
  } catch (error) {
    console.error('❌ ZAP is not running or not accessible at localhost:8080');
    console.error('   Please start ZAP proxy and try again.\n');
    return;
  }

  const scanContext = await fetchZapScanContext();

  console.log('=' .repeat(60));
  console.log('📊 SCAN CONTEXT SUMMARY:');
  console.log(`   Target URL: ${scanContext.targetUrl || 'Not available'}`);
  console.log(`   Sites scanned: ${scanContext.sites.length}`);
  console.log(`   URLs accessed: ${scanContext.urls.length}`);
  console.log(`   Hosts contacted: ${scanContext.hosts.length}`);
  console.log(`   Scan timestamp: ${scanContext.scanStartTime}`);
  console.log('=' .repeat(60));

  console.log('\n✅ Enhanced ZAP service now provides complete scan context!');
  console.log('   The JSON report saved to Google bucket will now include:');
  console.log('   - All scanned URLs and sites');
  console.log('   - Target information');
  console.log('   - Scan statistics');
  console.log('   - Vulnerability details with URL context');
}

// Run the test
main().catch(console.error);
