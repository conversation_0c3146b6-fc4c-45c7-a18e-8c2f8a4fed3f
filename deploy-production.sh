#!/bin/bash

# Production deployment script for WebSocket AI DAST Single Test service

set -e

echo "🚀 Starting production deployment..."

# Configuration
NAMESPACE=${K8S_NAMESPACE:-default}
IMAGE_TAG=${IMAGE_TAG:-latest}
REGISTRY=${DOCKER_REGISTRY:-your-registry}
IMAGE_NAME="websocket-ai-dast-single-test"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if docker is available
if ! command -v docker &> /dev/null; then
    print_error "docker is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

print_status "Prerequisites check passed"

# Build and push Docker image
echo "🏗️ Building Docker image..."
docker build -t ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} .

echo "📤 Pushing Docker image to registry..."
docker push ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}

print_status "Docker image built and pushed: ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

# Update image in deployment manifest
echo "📝 Updating deployment manifest with new image..."
sed -i.bak "s|your-registry/websocket-ai-dast-single-test:latest|${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}|g" k8s/production/websocket-deployment.yaml

# Apply Kubernetes manifests
echo "🚀 Deploying to Kubernetes..."

# Apply ConfigMap and Secrets first
kubectl apply -f k8s/production/websocket-config.yaml -n ${NAMESPACE}
print_status "ConfigMap and Secrets applied"

# Apply Deployment and Service
kubectl apply -f k8s/production/websocket-deployment.yaml -n ${NAMESPACE}
print_status "Deployment and Service applied"

# Wait for deployment to be ready
echo "⏳ Waiting for deployment to be ready..."
kubectl rollout status deployment/websocket-ai-dast-single-test -n ${NAMESPACE} --timeout=300s

# Check if pods are running
echo "🔍 Checking pod status..."
kubectl get pods -l app=websocket-ai-dast-single-test -n ${NAMESPACE}

# Get service information
echo "🌐 Service information:"
kubectl get service websocket-ai-dast-single-test-service -n ${NAMESPACE}

# Restore original deployment file
mv k8s/production/websocket-deployment.yaml.bak k8s/production/websocket-deployment.yaml

print_status "Deployment completed successfully!"

echo ""
echo "📊 Useful commands:"
echo "  View logs: kubectl logs -f deployment/websocket-ai-dast-single-test -n ${NAMESPACE}"
echo "  Scale deployment: kubectl scale deployment websocket-ai-dast-single-test --replicas=5 -n ${NAMESPACE}"
echo "  Check status: kubectl get pods -l app=websocket-ai-dast-single-test -n ${NAMESPACE}"
echo ""

print_status "Production deployment completed! 🎉"
