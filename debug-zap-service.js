#!/usr/bin/env node

/**
 * Debug script to test ZAP service connectivity
 * Usage: node debug-zap-service.js <client-id>
 */

const { exec } = require('child_process');
const axios = require('axios');

const CLIENT_ID = process.argv[2] || 'test-client-123';
const ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';
const NAMESPACE = process.env.K8S_NAMESPACE || 'default';

async function debugZapService() {
  console.log(`🔍 Debugging ZAP service for CLIENT_ID: ${CLIENT_ID}`);
  
  // Convert clientId to DNS-compliant name
  const dnsClientId = CLIENT_ID.toLowerCase().replace(/[_]/g, '-');
  const jobName = `zap-scan-${dnsClientId}`;
  const serviceName = `zap-service-${dnsClientId}`;
  const zapPort = 8080;
  
  console.log(`📝 Generated names:`);
  console.log(`   Job name: ${jobName}`);
  console.log(`   Service name: ${serviceName}`);
  console.log(`   DNS name: ${serviceName}.${NAMESPACE}.svc.cluster.local`);
  
  // Check if service exists
  console.log(`\n🔍 Checking if service exists...`);
  try {
    const serviceCheck = await execCommand(`kubectl get service ${serviceName} -n ${NAMESPACE} -o json`);
    const serviceData = JSON.parse(serviceCheck);
    console.log(`✅ Service exists: ${serviceName}`);
    console.log(`   Cluster IP: ${serviceData.spec.clusterIP}`);
    console.log(`   Ports: ${JSON.stringify(serviceData.spec.ports)}`);
    console.log(`   Selector: ${JSON.stringify(serviceData.spec.selector)}`);
  } catch (error) {
    console.log(`❌ Service not found: ${serviceName}`);
    return;
  }
  
  // Check if pods exist with correct labels
  console.log(`\n🔍 Checking for pods with job label...`);
  try {
    const podCheck = await execCommand(`kubectl get pods -l job=${jobName} -n ${NAMESPACE} -o json`);
    const podData = JSON.parse(podCheck);
    
    if (podData.items.length === 0) {
      console.log(`❌ No pods found with label job=${jobName}`);
      return;
    }
    
    console.log(`✅ Found ${podData.items.length} pod(s):`);
    podData.items.forEach((pod, index) => {
      console.log(`   Pod ${index + 1}: ${pod.metadata.name}`);
      console.log(`     Status: ${pod.status.phase}`);
      console.log(`     Labels: ${JSON.stringify(pod.metadata.labels)}`);
      console.log(`     Pod IP: ${pod.status.podIP}`);
    });
    
    // Test connectivity from within the cluster
    const podName = podData.items[0].metadata.name;
    console.log(`\n🔍 Testing connectivity from pod ${podName}...`);
    
    // Test localhost access (should work)
    try {
      const localhostTest = await execCommand(
        `kubectl exec ${podName} -n ${NAMESPACE} -- curl -s -m 10 "http://localhost:${zapPort}/JSON/core/view/version/?apikey=${ZAP_API_KEY}"`
      );
      console.log(`✅ Localhost access works: ${localhostTest}`);
    } catch (error) {
      console.log(`❌ Localhost access failed: ${error.message}`);
    }
    
    // Test service DNS access (this is what's failing)
    try {
      const serviceDnsTest = await execCommand(
        `kubectl exec ${podName} -n ${NAMESPACE} -- curl -s -m 10 "http://${serviceName}.${NAMESPACE}.svc.cluster.local:${zapPort}/JSON/core/view/version/?apikey=${ZAP_API_KEY}"`
      );
      console.log(`✅ Service DNS access works: ${serviceDnsTest}`);
    } catch (error) {
      console.log(`❌ Service DNS access failed: ${error.message}`);
    }
    
    // Test from outside the pod (this is what our WebSocket service does)
    console.log(`\n🔍 Testing external access to service...`);
    try {
      // This would require port-forward or being inside the cluster
      console.log(`⚠️ External access test requires port-forward or cluster access`);
      console.log(`   To test manually, run:`);
      console.log(`   kubectl port-forward service/${serviceName} ${zapPort}:${zapPort} -n ${NAMESPACE}`);
      console.log(`   Then: curl "http://localhost:${zapPort}/JSON/core/view/version/?apikey=${ZAP_API_KEY}"`);
    } catch (error) {
      console.log(`❌ External access test failed: ${error.message}`);
    }
    
  } catch (error) {
    console.log(`❌ Error checking pods: ${error.message}`);
  }
}

function execCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(new Error(`Command failed: ${command}\nError: ${error.message}\nStderr: ${stderr}`));
      } else {
        resolve(stdout.trim());
      }
    });
  });
}

// Run the debug script
debugZapService().catch(console.error);
