# Development Setup Guide

This guide helps you set up the ZAP WebSocket service for local development with Minikube.

## Prerequisites

1. **Node.js** (v16 or higher)
2. **Docker** (for Minikube)
3. **Minikube** 
4. **kubectl**

### Install Prerequisites (macOS)

```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install minikube kubectl docker
```

### Install Prerequisites (Linux)

```bash
# Install Minikube
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
```

## Quick Setup

1. **Clone and Install Dependencies**:
   ```bash
   cd websocket_ai_dast_single_test
   npm install
   ```

2. **Start Minikube**:
   ```bash
   minikube start --driver=docker --memory=4096 --cpus=2
   ```

3. **Configure Environment**:
   ```bash
   cp .env.minikube .env
   ```

4. **Pull ZAP Image**:
   ```bash
   minikube image pull zaproxy/zap-stable:latest
   ```

5. **Build the Project**:
   ```bash
   npm run build
   ```

6. **Test Kubernetes Connection**:
   ```bash
   node scripts/test-k8s-connection.js
   ```

7. **Start the Service**:
   ```bash
   npm run dev
   ```

## Detailed Setup

### 1. Minikube Configuration

Start Minikube with adequate resources:

```bash
# Start with Docker driver (recommended)
minikube start --driver=docker --memory=4096 --cpus=2

# Verify status
minikube status

# Set kubectl context
kubectl config use-context minikube
```

### 2. Environment Configuration

Copy the Minikube environment template:

```bash
cp .env.minikube .env
```

Key environment variables:

- `USE_KUBERNETES=true` - Enable Kubernetes mode
- `K8S_NAMESPACE=default` - Kubernetes namespace
- `ZAP_IMAGE=zaproxy/zap-stable:latest` - ZAP Docker image
- `ZAP_API_KEY=AgentqSuperAI` - ZAP API key

### 3. Pre-pull Docker Images

Pull the ZAP image to Minikube's Docker registry:

```bash
minikube image pull zaproxy/zap-stable:latest

# Verify the image is available
minikube image ls | grep zap
```

### 4. Build and Test

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Test Kubernetes connection
node scripts/test-k8s-connection.js
```

## Development Workflow

### Starting the Service

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

### Monitoring ZAP Jobs

```bash
# List all ZAP jobs
kubectl get jobs -l app=zap-scan

# Get job details
kubectl describe job zap-scan-<CLIENT_ID>

# View job logs
kubectl logs job/zap-scan-<CLIENT_ID>

# List ZAP services
kubectl get services -l app=zap-scan

# Watch pods in real-time
kubectl get pods -l app=zap-scan -w
```

### Debugging

1. **Check Service Logs**:
   ```bash
   # WebSocket service logs
   npm run dev
   ```

2. **Check Kubernetes Resources**:
   ```bash
   # All resources
   kubectl get all -l app=zap-scan
   
   # Events
   kubectl get events --sort-by=.metadata.creationTimestamp
   ```

3. **Access Pod Shell** (if needed):
   ```bash
   kubectl exec -it <pod-name> -- /bin/bash
   ```

### Cleanup

```bash
# Clean up all ZAP jobs
kubectl delete jobs -l app=zap-scan

# Clean up all ZAP services
kubectl delete services -l app=zap-scan

# Stop Minikube
minikube stop

# Delete Minikube cluster (if needed)
minikube delete
```

## Troubleshooting

### Common Issues

1. **"HTTP request failed" Error**:
   - Check if Minikube is running: `minikube status`
   - Verify kubectl context: `kubectl config current-context`
   - Test connection: `kubectl get nodes`

2. **ZAP Image Not Found**:
   - Pull image: `minikube image pull zaproxy/zap-stable:latest`
   - Verify: `minikube image ls | grep zap`

3. **Job Creation Fails**:
   - Check namespace: `kubectl get namespaces`
   - Check permissions: `kubectl auth can-i create jobs`
   - Check resource quotas: `kubectl describe quota`

4. **Pod Not Starting**:
   - Check pod status: `kubectl get pods -l app=zap-scan`
   - Check pod logs: `kubectl logs <pod-name>`
   - Check events: `kubectl describe pod <pod-name>`

### Fallback to Docker Mode

If Kubernetes is causing issues, you can fall back to Docker mode:

```bash
# Set in .env file
USE_KUBERNETES=false

# Restart the service
npm run dev
```

## Performance Tips

1. **Increase Minikube Resources**:
   ```bash
   minikube config set memory 8192
   minikube config set cpus 4
   minikube delete && minikube start
   ```

2. **Enable Minikube Addons**:
   ```bash
   minikube addons enable metrics-server
   minikube addons enable dashboard
   ```

3. **Monitor Resource Usage**:
   ```bash
   # View resource usage
   kubectl top nodes
   kubectl top pods -l app=zap-scan
   
   # Access dashboard
   minikube dashboard
   ```

## Next Steps

Once the development environment is working:

1. Test with multiple concurrent ZAP jobs
2. Monitor resource usage and performance
3. Configure production Kubernetes cluster
4. Set up monitoring and alerting
5. Implement CI/CD pipeline
