export type WSMessageType = 'auth' | 'command' | 'ping' | 'execute_test';

export interface WSBaseMessage {
  type: WSMessageType;
  token: string;
}

export interface WSAuthMessage extends WSBaseMessage {
  type: 'auth';
}

export interface WSCommandMessage extends WSBaseMessage {
  type: 'command';
  prompt: string;
  pageSource: string;
}

export interface WSPingMessage extends WSBaseMessage {
  type: 'ping';
}

export interface WSExecuteTestMessage extends WSBaseMessage {
  type: 'execute_test';
  testCaseId: string;
  tcId: string;
  steps: Array<{
    step: number;
    stepName: string;
    action?: string;
    target?: string;
    value?: string;
    prompt?: string;
  }>;
  testCase: {
    title: string;
    precondition?: string;
    expectation?: string;
    projectId?: string;
  };
  authToken?: string;
  projectId?: string;
  testRunId?: string;
  clientId?: string; // Add unique client ID to prevent job interference
}

export type WSMessage = WSAuthMessage | WSCommandMessage | WSPingMessage | WSExecuteTestMessage;

export interface AICommand {
  action: string;
  target: string;
  value?: string | boolean | number | null;
}

export interface WSResponse {
  type: 'response' | 'error' | 'auth_success' | 'pong' | 'complete' | 'test_start' | 'test_output' | 'test_complete' | 'test_error' | 'test_queued' | 'queue_status';
  command?: AICommand;
  message?: string;
  code?: number;
  testCaseId?: string;
  tcId?: string;
  output?: string;
  isError?: boolean;
  status?: 'passed' | 'failed' | 'waiting' | 'active' | 'completed';
  stdout?: string;
  stderr?: string;
  jobId?: string;
  position?: number;
  queueStats?: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    total: number;
  };
  timestamp?: number;
  apiKey?: string;
  error?: string;
}