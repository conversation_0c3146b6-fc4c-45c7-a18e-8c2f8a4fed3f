import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Express } from 'express';
import { Queue } from 'bullmq';
import { config } from 'dotenv';
import { QueueService } from './queue-service';

// Load environment variables
config();

// Redis configuration for Bull Board (same as queue service)
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null, // Required by BullMQ
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
  db: parseInt(process.env.REDIS_DB || '11'),
};

export class DashboardService {
  private static serverAdapter: ExpressAdapter;
  private static dashboardQueue: Queue;
  private static isInitialized = false;

  // Initialize Bull Board dashboard
  static initialize(app: Express): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create Express adapter for Bull Board
      this.serverAdapter = new ExpressAdapter();
      this.serverAdapter.setBasePath('/admin/queues');

      // Create a separate queue instance for the dashboard with proper auth
      this.dashboardQueue = new Queue('test-execution', {
        connection: redisConfig,
      });

      // Create Bull Board with queue adapters
      createBullBoard({
        queues: [
          new BullMQAdapter(this.dashboardQueue),
        ],
        serverAdapter: this.serverAdapter,
      });

      // Mount the Bull Board UI
      app.use('/admin/queues', this.serverAdapter.getRouter());

      console.log('🎯 Bull Board Dashboard initialized at /admin/queues');
      this.isInitialized = true;

    } catch (error) {
      console.error('Failed to initialize Bull Board Dashboard:', error);
      throw error;
    }
  }

  // Add queue statistics endpoint
  static addStatsEndpoint(app: Express): void {
    app.get('/api/queue/stats', async (req, res) => {
      try {
        const stats = await QueueService.getQueueStats();
        res.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to get queue stats:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get queue statistics',
        });
      }
    });

    app.get('/api/queue/position/:jobId', async (req, res) => {
      try {
        const { jobId } = req.params;
        const position = await QueueService.getJobPosition(jobId);
        res.json({
          success: true,
          data: { position },
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to get job position:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get job position',
        });
      }
    });

    app.get('/api/queue/running-test-cases', async (req, res) => {
      try {
        const runningTestCases = QueueService.getRunningTestCases();
        res.json({
          success: true,
          data: runningTestCases,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to get running test cases:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get running test cases',
        });
      }
    });

    // CLIENT_ID-specific queue statistics endpoint
    app.get('/api/queue/stats/:clientId', async (req, res) => {
      try {
        const { clientId } = req.params;
        const stats = await QueueService.getClientQueueStats(clientId);
        res.json(stats);
      } catch (error) {
        console.error('Failed to get CLIENT_ID-specific queue stats:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get CLIENT_ID-specific queue statistics',
        });
      }
    });

    console.log('📊 Queue statistics endpoints added (including CLIENT_ID-specific)');
  }

  // Cleanup dashboard resources
  static async cleanup(): Promise<void> {
    if (this.dashboardQueue) {
      try {
        await this.dashboardQueue.close();
        console.log('✅ Dashboard queue closed');
      } catch (error) {
        console.error('Error closing dashboard queue:', error);
      }
    }
  }
}
