import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Express } from 'express';
import { QueueService } from './queue-service';

export class DashboardService {
  private static serverAdapter: ExpressAdapter;
  private static isInitialized = false;

  static initialize(app: Express): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create the server adapter for Express
      this.serverAdapter = new ExpressAdapter();
      this.serverAdapter.setBasePath('/admin/queues');

      // Create Bull Board with the test run queue
      createBullBoard({
        queues: [new BullMQAdapter(QueueService.getQueue())],
        serverAdapter: this.serverAdapter,
      });

      // Mount the Bull Board UI
      app.use('/admin/queues', this.serverAdapter.getRouter());

      console.log('✅ Bull Board Dashboard initialized successfully (TestRun)');
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Bull Board Dashboard (TestRun):', error);
      // Don't throw - continue without dashboard
    }
  }

  static addStatsEndpoint(app: Express): void {
    // Add API endpoint for queue statistics
    app.get('/api/queue/stats', async (req, res) => {
      try {
        const stats = await QueueService.getQueueStats();
        res.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to get queue stats:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get queue statistics',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Add API endpoint for checking if queue is busy
    app.get('/api/queue/busy', async (req, res) => {
      try {
        const stats = await QueueService.getQueueStats();
        const isBusy = stats.active > 0;
        
        res.json({
          success: true,
          data: {
            busy: isBusy,
            active: stats.active,
            waiting: stats.waiting,
            message: isBusy 
              ? 'Queue is busy - test execution in progress' 
              : 'Queue is available for new tests'
          },
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to check queue status:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to check queue status',
          timestamp: new Date().toISOString(),
        });
      }
    });

    console.log('✅ Queue stats API endpoints added (TestRun)');
  }

  static getServerAdapter(): ExpressAdapter | null {
    return this.isInitialized ? this.serverAdapter : null;
  }
}
