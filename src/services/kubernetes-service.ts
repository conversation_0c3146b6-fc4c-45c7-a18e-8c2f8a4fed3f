import * as k8s from '@kubernetes/client-node';
import { ZapJobInfo } from './zap-service';

export class KubernetesService {
  private static k8sApi: k8s.BatchV1Api;
  private static coreApi: k8s.CoreV1Api;
  private static initialized = false;

  // Kubernetes configuration
  private static readonly NAMESPACE = process.env.K8S_NAMESPACE || 'default';
  private static readonly ZAP_IMAGE = process.env.ZAP_IMAGE || 'zaproxy/zap-stable:latest';
  private static readonly ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';

  // Port forwarding management
  private static portForwards: Map<string, { process: any, port: number }> = new Map();
  private static readonly PORT_FORWARD_RANGE_START = 9001;
  private static readonly PORT_FORWARD_RANGE_END = 10000;
  private static usedPorts: Set<number> = new Set();

  /**
   * Initialize Kubernetes client
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      const kc = new k8s.KubeConfig();

      // Load config based on environment
      if (process.env.NODE_ENV === 'production' && process.env.KUBERNETES_SERVICE_HOST) {
        // In-cluster configuration for production
        console.log('🔧 Loading in-cluster Kubernetes configuration...');
        kc.loadFromCluster();
      } else {
        // Local development - load from kubeconfig
        console.log('🔧 Loading Kubernetes configuration from kubeconfig...');
        kc.loadFromDefault();

        // For Minikube, ensure we're using the correct context
        const currentContext = kc.getCurrentContext();
        console.log(`📋 Current Kubernetes context: ${currentContext}`);

        // Verify connection by getting cluster info
        const cluster = kc.getCurrentCluster();
        if (cluster) {
          console.log(`🔗 Kubernetes cluster: ${cluster.name} at ${cluster.server}`);
        }
      }

      this.k8sApi = kc.makeApiClient(k8s.BatchV1Api);
      this.coreApi = kc.makeApiClient(k8s.CoreV1Api);

      // Test the connection
      await this.testConnection();

      this.initialized = true;
      console.log('✅ Kubernetes client initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Kubernetes client:', error);
      console.error('💡 Make sure Minikube is running: minikube start');
      console.error('💡 Check kubectl context: kubectl config current-context');
      throw error;
    }
  }

  /**
   * Test Kubernetes connection
   */
  private static async testConnection(): Promise<void> {
    try {
      // Try to list namespaces to test connection
      const namespaces = await this.coreApi.listNamespace();
      console.log(`✅ Kubernetes connection test passed. Found ${namespaces.body.items.length} namespaces.`);

      // Check if our target namespace exists
      const targetNamespace = namespaces.body.items.find(ns => ns.metadata?.name === this.NAMESPACE);
      if (targetNamespace) {
        console.log(`✅ Target namespace '${this.NAMESPACE}' exists`);
      } else {
        console.warn(`⚠️ Target namespace '${this.NAMESPACE}' not found. Available namespaces: ${namespaces.body.items.map(ns => ns.metadata?.name).join(', ')}`);
      }
    } catch (error: any) {
      console.error('❌ Kubernetes connection test failed:', error);
      console.error('💡 GKE troubleshooting steps:');
      console.error('   - Check cluster credentials: gcloud container clusters get-credentials CLUSTER_NAME --zone=ZONE');
      console.error('   - Verify kubectl context: kubectl config current-context');
      console.error('   - Test kubectl access: kubectl get nodes');
      console.error('   - Check service account permissions if running in-cluster');
      
      // Log more detailed error information
      if (error.response) {
        console.error(`   - HTTP Status: ${error.response.statusCode}`);
        console.error(`   - Response: ${JSON.stringify(error.response.body, null, 2)}`);
      }
      
      throw new Error(`Kubernetes connection test failed: ${error.message || error}`);
    }
  }

  /**
   * Create a ZAP Job for security scanning
   */
  static async createZapJob(clientId: string): Promise<ZapJobInfo | null> {
    await this.initialize();

    try {
      // Convert clientId to DNS-compliant name (replace underscores with hyphens, ensure lowercase)
      const dnsClientId = clientId.toLowerCase().replace(/[_]/g, '-');
      const jobName = `zap-scan-${dnsClientId}`;
      const serviceName = `zap-service-${dnsClientId}`;
      const zapPort = 8080; // Fixed port inside the pod

      console.log(`🚀 Creating ZAP Job for CLIENT_ID: ${clientId} (DNS name: ${dnsClientId})`);

      // Create Service first to expose the ZAP API
      const service: k8s.V1Service = {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: serviceName,
          namespace: this.NAMESPACE,
          labels: {
            app: 'zap-scan',
            clientId: dnsClientId,
            component: 'security-testing'
          }
        },
        spec: {
          selector: {
            job: jobName,
            app: 'zap-scan',
            clientId: dnsClientId
          },
          ports: [{
            port: zapPort,
            targetPort: zapPort,
            protocol: 'TCP'
          }],
          type: 'ClusterIP' // Use ClusterIP for internal communication
        }
      };

      await this.coreApi.createNamespacedService(this.NAMESPACE, service);
      console.log(`✅ Service created: ${serviceName}`);

      // Create Job for ZAP container
      const job: k8s.V1Job = {
        apiVersion: 'batch/v1',
        kind: 'Job',
        metadata: {
          name: jobName,
          namespace: this.NAMESPACE,
          labels: {
            app: 'zap-scan',
            clientId: dnsClientId,
            component: 'security-testing'
          }
        },
        spec: {
          template: {
            metadata: {
              labels: {
                job: jobName,
                app: 'zap-scan',
                clientId: dnsClientId
              }
            },
            spec: {
              restartPolicy: 'Never',
              containers: [{
                name: 'zap-container',
                image: this.ZAP_IMAGE,
                imagePullPolicy: 'IfNotPresent',
                command: ['zap.sh'],
                args: [
                  '-daemon',
                  '-host', '0.0.0.0',
                  '-port', zapPort.toString(),
                  '-config', 'api.addrs.addr.name=.*',
                  '-config', 'api.addrs.addr.regex=true',
                  '-config', `api.key=${this.ZAP_API_KEY}`,
                  // Prevent ZAP from proxying its own API calls
                  '-config', 'network.connection.timeoutInSecs=30',
                  '-config', 'network.connection.defaultUserAgent=ZAP',
                  // Configure ZAP to not proxy localhost traffic
                  '-config', 'network.localServers.mainProxy.excludedDomains=localhost,127.0.0.1'
                ],
                ports: [{
                  containerPort: zapPort,
                  protocol: 'TCP'
                }],
                resources: {
                  requests: {
                    memory: '2Gi',
                    cpu: '1'
                  },
                  limits: {
                    memory: '3Gi',
                    cpu: '2'
                  }
                },
                env: [{
                  name: 'CLIENT_ID',
                  value: clientId
                }, {
                  name: 'DNS_CLIENT_ID',
                  value: dnsClientId
                }, {
                  name: 'ZAP_PORT',
                  value: zapPort.toString()
                }, {
                  name: 'ZAP_API_KEY',
                  value: this.ZAP_API_KEY
                }],
                // Add readiness probe to know when ZAP is ready
                readinessProbe: {
                  httpGet: {
                    path: '/JSON/core/view/version/',
                    port: zapPort,
                    httpHeaders: [{
                      name: 'X-ZAP-API-Key',
                      value: this.ZAP_API_KEY
                    }]
                  },
                  initialDelaySeconds: 45, // Increased initial delay for ZAP startup
                  periodSeconds: 10,       // Check every 10 seconds
                  timeoutSeconds: 30,      // 30 second timeout per probe (matches our API timeout)
                  failureThreshold: 6      // Allow up to 3 minutes total for startup (6 * 30s)
                },
                // Add liveness probe to restart if ZAP becomes unresponsive
                livenessProbe: {
                  httpGet: {
                    path: '/JSON/core/view/version/',
                    port: zapPort,
                    httpHeaders: [{
                      name: 'X-ZAP-API-Key',
                      value: this.ZAP_API_KEY
                    }]
                  },
                  initialDelaySeconds: 120, // Wait 2 minutes before starting liveness checks
                  periodSeconds: 30,        // Check every 30 seconds
                  timeoutSeconds: 30,       // 30 second timeout per probe
                  failureThreshold: 3       // Restart after 3 consecutive failures
                }
              }]
            }
          },
          backoffLimit: 1, // Don't retry failed jobs
          ttlSecondsAfterFinished: 300 // Clean up completed jobs after 5 minutes
        }
      };

      await this.k8sApi.createNamespacedJob(this.NAMESPACE, job);
      console.log(`✅ ZAP Job created: ${jobName}`);

      // Wait for the pod to be ready
      const podReady = await this.waitForPodReady(jobName, 120); // 2 minute timeout
      if (!podReady) {
        throw new Error(`ZAP pod failed to become ready within timeout`);
      }

      // Determine host based on environment
      const host = this.getZapServiceHost(serviceName, zapPort);
      console.log(`✅ ZAP service accessible at: ${host}`);

      // Verify service is properly created and accessible
      await this.verifyServiceConnectivity(serviceName, zapPort, host);

      const zapJobInfo: ZapJobInfo = {
        jobName,
        serviceName,
        namespace: this.NAMESPACE,
        port: zapPort, // Use the service port
        host: host,
        clientId,
        createdAt: new Date()
      };

      console.log(`✅ ZAP Job ready for CLIENT_ID: ${clientId} at ${zapJobInfo.host}`);
      return zapJobInfo;

    } catch (error) {
      console.error(`❌ Failed to create ZAP Job for CLIENT_ID ${clientId}:`, error);
      
      // Cleanup on failure
      try {
        await this.deleteZapJob(clientId);
      } catch (cleanupError) {
        console.warn(`⚠️ Failed to cleanup after job creation failure:`, cleanupError);
      }
      
      return null;
    }
  }

  /**
   * Wait for pod to be ready
   */
  private static async waitForPodReady(jobName: string, timeoutSeconds: number): Promise<boolean> {
    const startTime = Date.now();
    const timeout = timeoutSeconds * 1000;

    while (Date.now() - startTime < timeout) {
      try {
        const pods = await this.coreApi.listNamespacedPod(
          this.NAMESPACE,
          undefined, undefined, undefined, undefined,
          `job-name=${jobName}`
        );

        if (pods.body.items.length > 0) {
          const pod = pods.body.items[0];
          const podStatus = pod.status;

          if (podStatus?.phase === 'Running') {
            // Check if all containers are ready
            const containerStatuses = podStatus.containerStatuses || [];
            const allReady = containerStatuses.every(status => status.ready);
            
            if (allReady) {
              console.log(`✅ Pod ready for job: ${jobName}`);
              return true;
            }
          }
        }

        // Wait 2 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.warn(`⚠️ Error checking pod status for job ${jobName}:`, error);
      }
    }

    console.error(`❌ Pod failed to become ready within ${timeoutSeconds} seconds for job: ${jobName}`);
    return false;
  }

  /**
   * Delete ZAP Job and associated resources
   */
  static async deleteZapJob(clientId: string): Promise<void> {
    await this.initialize();

    // Convert clientId to DNS-compliant name (same as in createZapJob)
    const dnsClientId = clientId.toLowerCase().replace(/[_]/g, '-');
    const jobName = `zap-scan-${dnsClientId}`;
    const serviceName = `zap-service-${dnsClientId}`;

    try {
      console.log(`🗑️ Deleting ZAP Job and Service for CLIENT_ID: ${clientId}`);

      // Clean up port forwarding first
      this.cleanupPortForward(serviceName);

      // Delete Job with force cleanup for running jobs
      try {
        // First, try to delete gracefully
        await this.k8sApi.deleteNamespacedJob(
          jobName,
          this.NAMESPACE,
          undefined,
          undefined,
          undefined,
          undefined,
          'Background' // Delete in background
        );
        console.log(`✅ Job deleted: ${jobName}`);
      } catch (error: any) {
        if (error.response?.statusCode !== 404) {
          console.warn(`⚠️ Error deleting job ${jobName}:`, error.message);

          // If graceful deletion fails, try force deletion
          try {
            console.log(`🔨 Force deleting job ${jobName}...`);
            await this.k8sApi.deleteNamespacedJob(
              jobName,
              this.NAMESPACE,
              undefined,
              undefined,
              0, // Grace period = 0 for immediate deletion
              undefined,
              'Foreground' // Force foreground deletion
            );
            console.log(`✅ Job force deleted: ${jobName}`);
          } catch (forceError: any) {
            if (forceError.response?.statusCode !== 404) {
              console.error(`❌ Force deletion also failed for ${jobName}:`, forceError.message);
            }
          }
        }
      }

      // Delete Service
      try {
        await this.coreApi.deleteNamespacedService(serviceName, this.NAMESPACE);
        console.log(`✅ Service deleted: ${serviceName}`);
      } catch (error: any) {
        if (error.response?.statusCode !== 404) {
          console.warn(`⚠️ Error deleting service ${serviceName}:`, error.message);
        }
      }

      console.log(`🧹 ZAP Job cleanup completed for CLIENT_ID: ${clientId}`);
    } catch (error) {
      console.error(`❌ Error during ZAP Job cleanup for CLIENT_ID ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * List all ZAP Jobs
   */
  static async listZapJobs(): Promise<string[]> {
    await this.initialize();

    try {
      const jobs = await this.k8sApi.listNamespacedJob(
        this.NAMESPACE,
        undefined, undefined, undefined, undefined,
        'app=zap-scan'
      );

      return jobs.body.items.map(job => job.metadata?.name || '').filter(name => name);
    } catch (error) {
      console.error('❌ Error listing ZAP Jobs:', error);
      return [];
    }
  }

  /**
   * Cleanup orphaned ZAP Jobs (older than specified minutes)
   */
  static async cleanupOrphanedJobs(olderThanMinutes: number = 10): Promise<void> {
    await this.initialize();

    try {
      console.log(`🧹 Cleaning up ZAP Jobs older than ${olderThanMinutes} minutes...`);

      const jobs = await this.k8sApi.listNamespacedJob(
        this.NAMESPACE,
        undefined, undefined, undefined, undefined,
        'app=zap-scan'
      );

      const now = Date.now();
      const cutoffTime = now - (olderThanMinutes * 60 * 1000);

      for (const job of jobs.body.items) {
        const creationTime = job.metadata?.creationTimestamp;
        if (creationTime) {
          const jobTime = new Date(creationTime).getTime();
          if (jobTime < cutoffTime) {
            const jobName = job.metadata?.name;
            const clientId = job.metadata?.labels?.clientId;
            
            if (jobName && clientId) {
              console.log(`🗑️ Removing orphaned ZAP Job: ${jobName} (${((now - jobTime) / 60000).toFixed(1)}min old)`);
              await this.deleteZapJob(clientId);
            }
          }
        }
      }

      console.log('✅ Orphaned ZAP Jobs cleanup completed');
    } catch (error) {
      console.error('❌ Error during orphaned jobs cleanup:', error);
    }
  }

  /**
   * Set up port forwarding for ZAP service
   */
  private static async setupPortForward(serviceName: string, servicePort: number): Promise<number> {
    const { spawn } = require('child_process');

    // Find an available local port
    const localPort = await this.findAvailablePort();

    try {
      console.log(`🔗 Setting up port forward: localhost:${localPort} -> ${serviceName}:${servicePort}`);

      // Use kubectl port-forward to forward the service
      const portForwardProcess = spawn('kubectl', [
        'port-forward',
        `service/${serviceName}`,
        `${localPort}:${servicePort}`,
        '-n', this.NAMESPACE
      ], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      // Store the process for cleanup
      this.portForwards.set(serviceName, { process: portForwardProcess, port: localPort });
      this.usedPorts.add(localPort);

      // Wait for port forward to be ready
      await new Promise<void>((resolve, reject) => {
        let resolved = false;
        const timeout = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            reject(new Error('Port forward setup timeout'));
          }
        }, 30000); // 30 second timeout

        portForwardProcess.stdout.on('data', (data: Buffer) => {
          const output = data.toString();
          console.log(`📡 Port forward output: ${output.trim()}`);
          if (output.includes('Forwarding from') && !resolved) {
            resolved = true;
            clearTimeout(timeout);
            resolve();
          }
        });

        portForwardProcess.stderr.on('data', (data: Buffer) => {
          const error = data.toString();
          console.warn(`⚠️ Port forward stderr: ${error.trim()}`);
          if (error.includes('error') && !resolved) {
            resolved = true;
            clearTimeout(timeout);
            reject(new Error(`Port forward failed: ${error}`));
          }
        });

        portForwardProcess.on('exit', (code: number) => {
          if (code !== 0 && !resolved) {
            resolved = true;
            clearTimeout(timeout);
            reject(new Error(`Port forward process exited with code ${code}`));
          }
        });
      });

      console.log(`✅ Port forward ready: localhost:${localPort} -> ${serviceName}:${servicePort}`);
      return localPort;

    } catch (error) {
      // Cleanup on failure
      this.usedPorts.delete(localPort);
      this.portForwards.delete(serviceName);
      console.error(`❌ Failed to set up port forward for ${serviceName}:`, error);
      throw error;
    }
  }

  /**
   * Verify that the ZAP service is properly created and accessible
   */
  private static async verifyServiceConnectivity(serviceName: string, port: number, host: string): Promise<void> {
    try {
      console.log(`🔍 Verifying service connectivity for ${serviceName}...`);

      // Check if service exists in Kubernetes
      const service = await this.coreApi.readNamespacedService(serviceName, this.NAMESPACE);
      console.log(`✅ Service ${serviceName} exists in namespace ${this.NAMESPACE}`);
      console.log(`📊 Service selector:`, service.body.spec?.selector);
      console.log(`📊 Service ports:`, service.body.spec?.ports);

      // List pods that should match the service selector
      const pods = await this.coreApi.listNamespacedPod(
        this.NAMESPACE,
        undefined, // pretty
        undefined, // allowWatchBookmarks
        undefined, // continue
        undefined, // fieldSelector
        `job=${serviceName.replace('zap-service-', 'zap-scan-')}` // labelSelector
      );

      console.log(`📊 Found ${pods.body.items.length} pods matching service selector`);
      pods.body.items.forEach(pod => {
        console.log(`  - Pod: ${pod.metadata?.name}, Status: ${pod.status?.phase}, Ready: ${pod.status?.conditions?.find(c => c.type === 'Ready')?.status}`);
      });

    } catch (error) {
      console.warn(`⚠️ Service connectivity verification failed for ${serviceName}:`, error);
    }
  }

  /**
   * Get the correct host for ZAP service based on environment
   * - In production: Use Kubernetes DNS for in-cluster communication
   * - In development: Use localhost with tunnel or Minikube IP
   */
  private static getZapServiceHost(serviceName: string, port: number): string {
    // Check if running in Kubernetes cluster (production)
    if (process.env.NODE_ENV === 'production' && process.env.KUBERNETES_SERVICE_HOST) {
      // In Kubernetes, use cluster DNS for service discovery
      // Format: http://service-name.namespace.svc.cluster.local:port
      const host = `http://${serviceName}.${this.NAMESPACE}.svc.cluster.local:${port}`;
      console.log(`🔧 Production environment detected - using Kubernetes DNS: ${host}`);
      return host;
    } else {
      // Development environment - use localhost with tunnel
      const host = `http://localhost:${port}`;
      console.log(`🔧 Development environment - using localhost with tunnel: ${host}`);
      return host;
    }
  }

  /**
   * Check if Minikube tunnel is running by checking for tunnel processes
   */
  private static async checkMinikubeTunnelStatus(): Promise<boolean> {
    const { exec } = require('child_process');

    try {
      // Check if minikube tunnel process is running
      const tunnelCheck = await new Promise<boolean>((resolve) => {
        exec('ps aux | grep "minikube tunnel" | grep -v grep', (error: any, stdout: string) => {
          if (error) {
            resolve(false);
          } else {
            const hasTunnelProcess = stdout.trim().length > 0;
            resolve(hasTunnelProcess);
          }
        });
      });

      return tunnelCheck;
    } catch (error) {
      console.warn(`⚠️ Error checking tunnel process: ${error}`);
      return false;
    }
  }

  /**
   * Get Minikube IP address
   */
  private static async getMinikubeIp(): Promise<string> {
    const { exec } = require('child_process');

    try {
      const minikubeIp = await new Promise<string>((resolve, reject) => {
        exec('minikube ip', (error: any, stdout: string, _stderr: string) => {
          if (error) {
            reject(new Error(`Failed to get Minikube IP: ${error.message}`));
          } else {
            const ip = stdout.trim();
            if (ip && ip.match(/^\d+\.\d+\.\d+\.\d+$/)) {
              resolve(ip);
            } else {
              reject(new Error(`Invalid Minikube IP format: ${ip}`));
            }
          }
        });
      });

      console.log(`📍 Minikube IP: ${minikubeIp}`);
      return minikubeIp;
    } catch (error) {
      console.warn(`⚠️ Failed to get Minikube IP, falling back to localhost: ${error}`);
      return 'localhost'; // Fallback to localhost
    }
  }

  /**
   * Find an available NodePort (30000-32767 range)
   */
  private static async findAvailableNodePort(): Promise<number> {
    const net = require('net');

    // NodePort range in Kubernetes is 30000-32767
    for (let port = 30000; port <= 32767; port++) {
      if (!this.usedPorts.has(port)) {
        // Test if port is actually available on the Minikube node
        const isAvailable = await new Promise<boolean>((resolve) => {
          const server = net.createServer();
          server.listen(port, () => {
            server.close(() => resolve(true));
          });
          server.on('error', () => resolve(false));
        });

        if (isAvailable) {
          return port;
        }
      }
    }

    throw new Error('No available NodePorts (30000-32767)');
  }

  /**
   * Find an available port for port forwarding
   */
  private static async findAvailablePort(): Promise<number> {
    const net = require('net');

    for (let port = this.PORT_FORWARD_RANGE_START; port <= this.PORT_FORWARD_RANGE_END; port++) {
      if (!this.usedPorts.has(port)) {
        // Test if port is actually available
        const isAvailable = await new Promise<boolean>((resolve) => {
          const server = net.createServer();
          server.listen(port, () => {
            server.close(() => resolve(true));
          });
          server.on('error', () => resolve(false));
        });

        if (isAvailable) {
          return port;
        }
      }
    }

    throw new Error('No available ports for port forwarding');
  }

  /**
   * Clean up port forwarding for a service
   */
  private static cleanupPortForward(serviceName: string): void {
    const portForward = this.portForwards.get(serviceName);
    if (portForward) {
      try {
        console.log(`🔌 Cleaning up port forward for ${serviceName} (port ${portForward.port})`);
        portForward.process.kill('SIGTERM');
        this.usedPorts.delete(portForward.port);
        this.portForwards.delete(serviceName);
        console.log(`✅ Port forward cleaned up for ${serviceName}`);
      } catch (error) {
        console.warn(`⚠️ Error cleaning up port forward for ${serviceName}:`, error);
      }
    }
  }
}
