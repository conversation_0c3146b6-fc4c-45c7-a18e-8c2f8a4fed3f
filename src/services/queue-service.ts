import { Queue, Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import { config } from 'dotenv';
import { TestRunnerService } from './test-runner';

// Load environment variables
config();

// Redis connection configuration for BullMQ
const redisConfig = {
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null, // Required by BullMQ
  retryDelayOnFailover: 1000,
  enableReadyCheck: false,
  lazyConnect: true,
  connectTimeout: 10000,
  db: parseInt(process.env.REDIS_DB || '6'),
};

// Debug Redis configuration
console.log('🔧 Redis Config Debug:', {
  host: redisConfig.host,
  port: redisConfig.port,
  password: redisConfig.password ? '***' : 'undefined',
  db: redisConfig.db
});

// Create Redis connection with error handling
const connection = new Redis(redisConfig);

// Create a separate connection for the worker to avoid conflicts
const workerConnection = new Redis(redisConfig);

// Enhanced Redis error handling
connection.on('error', (err) => {
  if (err.message.includes('NOAUTH')) {
    console.error('❌ Redis Authentication Error: Please check REDIS_PASSWORD in .env file');
    console.log('💡 Current Redis config:', {
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password ? '***' : 'undefined',
      db: redisConfig.db
    });
  } else {
    console.error('❌ Redis connection error:', err.message);
  }
});

connection.on('connect', () => {
  console.log('✅ Connected to Redis successfully');
});

connection.on('ready', () => {
  console.log('✅ Redis is ready to accept commands');
});

// Test execution job data interface
interface TestJobData {
  apiKey: string;
  testData: {
    testCaseId: string;
    tcId: string;
    steps: any[];
    testCase: any;
    authToken?: string;
    projectId?: string;
    testRunId?: string;
  };
  clientId: string;
  timestamp: number;
}

export class QueueService {
  private static testQueue: Queue<TestJobData>;
  private static worker: Worker<TestJobData>;
  private static isInitialized = false;

  // Track running test cases for monitoring (no blocking for SaaS scalability)
  private static runningTestCases = new Set<string>();

  // Track running jobs by CLIENT_ID to provide CLIENT_ID-specific queue stats
  private static runningJobsByClientId = new Map<string, Set<string>>();

  // Track waiting jobs by CLIENT_ID
  private static waitingJobsByClientId = new Map<string, Set<string>>();

  // Initialize the queue system
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create the test execution queue with concurrency of 1
      this.testQueue = new Queue<TestJobData>('test-execution', {
        connection: connection, // Use connection instance
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 20,     // Keep last 20 failed jobs
          attempts: 0,          // Retry failed jobs up to 0 times
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          delay: 0,
          priority: 0,
        },
      });

      // Create worker with high concurrency for SaaS scalability
      this.worker = new Worker<TestJobData>(
        'test-execution',
        async (job: Job<TestJobData>) => {
          return await this.processTestJob(job);
        },
        {
          connection: connection, // Use same connection instance
          concurrency: 50, // Increased to 50 for SaaS scalability (each test uses isolated ZAP container)
          stalledInterval: 60 * 1000, // Check for stalled jobs every 60 seconds (default: 30s)
          maxStalledCount: 3, // Allow job to be stalled 3 times before failing (default: 1)
        }
      );

      // Worker event handlers
      this.worker.on('completed', async (job) => {
        console.log(`✅ Test job ${job.id} completed for API key: ${job.data.apiKey}`);
        await this.notifyJobStatus(job.data.clientId, 'completed', job.data);
      });

      this.worker.on('failed', async (job, err) => {
        console.error(`❌ Test job ${job?.id} failed for API key: ${job?.data.apiKey}`, err);
        if (job) {
          await this.notifyJobStatus(job.data.clientId, 'failed', job.data, err.message);
        }
      });

      this.worker.on('active', async (job) => {
        console.log(`🔄 Test job ${job.id} started for API key: ${job.data.apiKey}`);
        await this.notifyJobStatus(job.data.clientId, 'active', job.data);
      });

      // Add worker error and stalled event handlers for debugging
      this.worker.on('error', (err) => {
        console.error('🚨 Worker error:', err);
      });

      this.worker.on('stalled', (jobId) => {
        console.warn(`⚠️ Job ${jobId} stalled`);
      });

      this.worker.on('ready', () => {
        console.log('🟢 Worker is ready to process jobs');
      });

      // Add more debugging events
      this.worker.on('drained', () => {
        console.log('🏁 Worker drained - no more jobs to process');
      });

      this.worker.on('paused', () => {
        console.log('⏸️ Worker paused');
      });

      this.worker.on('resumed', () => {
        console.log('▶️ Worker resumed');
      });

      // Queue event handlers - suppress error logging
      this.testQueue.on('error', () => {
        // Silently ignore queue errors - they don't affect core functionality
      });

      console.log('🚀 BullMQ Queue Service initialized successfully');
      this.isInitialized = true;

      // Start periodic cleanup to prevent stale jobs
      this.startPeriodicCleanup();

    } catch (error) {
      console.error('Failed to initialize Queue Service:', error);
      throw error;
    }
  }

  // Add a test job to the queue
  static async addTestJob(
    apiKey: string,
    testData: any,
    clientId: string
  ): Promise<{ jobId: string; position: number }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Track test case for monitoring (no blocking for SaaS scalability)
      const testCaseId = testData.testCaseId;
      // Removed blocking logic - multiple customers can run the same test case simultaneously

      const jobData: TestJobData = {
        apiKey,
        testData,
        clientId,
        timestamp: Date.now(),
      };

      // Get queue stats for logging (no artificial delays for SaaS scalability)
      const activeBefore = await this.testQueue.getActive();
      const waitingBefore = await this.testQueue.getWaiting();

      const job = await this.testQueue.add(
        `test-${apiKey}-${Date.now()}`,
        jobData,
        {
          // No artificial delays - let BullMQ handle concurrency naturally for SaaS scalability
          delay: 0,
        }
      );

      // Track this job as waiting for the specific CLIENT_ID
      this.addWaitingJobForClient(clientId, job.id!);
      console.log(`📋 Job ${job.id} added to waiting queue for CLIENT_ID: ${clientId}`);

      // Debug: Check if job was actually added to the queue
      console.log(`🔍 Job ${job.id} added with status:`, await job.getState());

      // Force check queue status after adding job
      setTimeout(async () => {
        const waiting = await this.testQueue.getWaiting();
        const active = await this.testQueue.getActive();
        console.log(`🔍 Queue check after adding job ${job.id}: ${waiting.length} waiting, ${active.length} active`);
        if (waiting.length > 0) {
          console.log(`🔍 Waiting jobs:`, waiting.map(j => j.id));
        }
      }, 500);

      // Calculate position: the job was added after existing jobs
      const totalJobsAhead = activeBefore.length + waitingBefore.length;
      const position = totalJobsAhead + 1; // Position in the overall queue

      console.log(`📝 Test job ${job.id} added to queue for API key: ${apiKey}, position: ${position}`);
      console.log(`📊 Queue status: ${activeBefore.length} active, ${waitingBefore.length} waiting`);

      // Notify client about job being queued
      if (totalJobsAhead > 0) {
        // Job is waiting because there are other jobs ahead
        await this.notifyJobStatus(clientId, 'waiting', jobData);
        console.log(`⏳ Test job ${job.id} is waiting in queue at position ${position} (${activeBefore.length} active, ${waitingBefore.length} waiting ahead)`);
      } else {
        // Job will start immediately
        console.log(`🚀 Test job ${job.id} will start immediately`);
      }

      return {
        jobId: job.id!,
        position: position,
      };

    } catch (error) {
      console.error('Failed to add test job to queue:', error);
      throw error;
    }
  }

  // Process a test job
  private static async processTestJob(job: Job<TestJobData>): Promise<void> {
    const { apiKey, testData, clientId } = job.data;
    const testCaseId = testData.testCaseId;

    try {
      console.log(`🔄 Processing test job ${job.id} for API key: ${apiKey}, client: ${clientId}`);

      // Move job from waiting to running for CLIENT_ID tracking
      this.removeWaitingJobForClient(clientId, job.id!);
      this.addRunningJobForClient(clientId, job.id!);
      console.log(`🏃 Job ${job.id} moved to running for CLIENT_ID: ${clientId}`);

      // Add test case to running set
      if (testCaseId) {
        this.runningTestCases.add(testCaseId);
        console.log(`🔒 Test case ${testCaseId} marked as running`);
      }

      // Update job progress
      await job.updateProgress(10);

      // Don't check for client connection - let the test runner handle it
      // The queue should process jobs regardless of WebSocket connection state
      console.log(`🚀 Executing test job ${job.id} for API key: ${apiKey}, client: ${clientId} (connection-independent)`);

      // Note: TestRunnerService.executeTest will handle missing WebSocket connections gracefully

      await job.updateProgress(25);

      // Execute the test using the existing TestRunnerService with clientId for message filtering
      // Add timeout to prevent hanging jobs and progress updates to prevent stalling
      const testExecutionPromise = TestRunnerService.executeTest(apiKey, testData, clientId);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Test execution timeout after 10 minutes'));
        }, 10 * 60 * 1000); // 10 minute timeout
      });

      // Start progress updates to prevent job stalling
      const progressInterval = setInterval(async () => {
        try {
          await job.updateProgress(50); // Keep job alive with progress updates
          console.log(`🔄 Progress update for job ${job.id} to prevent stalling`);
        } catch (error) {
          console.error(`Failed to update progress for job ${job.id}:`, error);
        }
      }, 30 * 1000); // Update every 30 seconds

      try {
        await Promise.race([testExecutionPromise, timeoutPromise]);
      } finally {
        clearInterval(progressInterval); // Stop progress updates
      }

      await job.updateProgress(100);

      console.log(`✅ Test job ${job.id} completed successfully for API key: ${apiKey}, client: ${clientId}`);

    } catch (error) {
      console.error(`❌ Test job ${job.id} failed for API key: ${apiKey}, client: ${clientId}:`, error);
      throw error;
    } finally {
      // Remove test case from monitoring set when job completes (success or failure)
      if (testCaseId) {
        this.runningTestCases.delete(testCaseId);
        console.log(`🔓 Test case ${testCaseId} removed from running set`);
      }

      // Remove job from CLIENT_ID tracking (success or failure)
      this.removeRunningJobForClient(clientId, job.id!);
      console.log(`🧹 Job ${job.id} removed from running for CLIENT_ID: ${clientId}`);
    }
  }

  // Notify client about job status changes
  private static async notifyJobStatus(
    clientId: string,
    status: 'waiting' | 'active' | 'completed' | 'failed',
    jobData: TestJobData,
    error?: string
  ): Promise<void> {
    try {
      // Use clientId to get the specific WebSocket connection
      const ws = TestRunnerService.getClientConnection(clientId);
      if (ws && ws.readyState === 1) { // WebSocket.OPEN
        console.log(`📤 Notifying client ${clientId} about job status: ${status}`);

        // Get current queue stats
        const queueStats = await this.getQueueStats();

        ws.send(JSON.stringify({
          type: 'queue_status',
          status,
          jobId: clientId,
          apiKey: jobData.apiKey,
          timestamp: Date.now(),
          error: error || undefined,
          queueStats: queueStats,
        }));
      } else {
        console.log(`⚠️ No active WebSocket connection found for client ${clientId}`);
      }
    } catch (err) {
      console.error('Failed to notify client about job status:', err);
    }
  }

  // Get queue statistics
  static async getQueueStats(): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const waiting = await this.testQueue.getWaiting();
      const active = await this.testQueue.getActive();
      const completed = await this.testQueue.getCompleted();
      const failed = await this.testQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length,
      };
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return { waiting: 0, active: 0, completed: 0, failed: 0, total: 0 };
    }
  }

  // Get job position in queue
  static async getJobPosition(jobId: string): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const waiting = await this.testQueue.getWaiting();
      const position = waiting.findIndex(job => job.id === jobId) + 1;
      return position || 0;
    } catch (error) {
      console.error('Failed to get job position:', error);
      return 0;
    }
  }

  // Clean up completed and failed jobs
  static async cleanQueue(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Clean old completed and failed jobs
      await this.testQueue.clean(24 * 60 * 60 * 1000, 10, 'completed'); // Keep completed jobs for 24 hours
      await this.testQueue.clean(7 * 24 * 60 * 60 * 1000, 20, 'failed'); // Keep failed jobs for 7 days

      // Clean stale active jobs (jobs that have been active for more than 15 minutes)
      const activeJobs = await this.testQueue.getActive();
      const now = Date.now();

      for (const job of activeJobs) {
        const jobAge = now - job.timestamp;
        if (jobAge > 15 * 60 * 1000) { // 15 minutes
          console.log(`🧹 Removing stale active job ${job.id} (age: ${Math.round(jobAge / 60000)} minutes)`);
          try {
            await job.moveToFailed(new Error('Job timeout - exceeded maximum execution time'), '0');
          } catch (moveError) {
            console.error(`Failed to move stale job ${job.id} to failed:`, moveError);
          }
        }
      }

      console.log('🧹 Queue cleaned successfully');
    } catch (error) {
      console.error('Failed to clean queue:', error);
    }
  }

  // Start periodic queue cleanup
  static startPeriodicCleanup(): void {
    // Clean queue every 5 minutes
    setInterval(async () => {
      try {
        await this.cleanQueue();
      } catch (error) {
        console.error('Periodic queue cleanup failed:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    console.log('🔄 Periodic queue cleanup started (every 5 minutes)');
  }

  // Get the queue instance for Bull Board
  static getQueue(): Queue<TestJobData> {
    return this.testQueue;
  }

  // Get list of currently running test cases
  static getRunningTestCases(): string[] {
    return Array.from(this.runningTestCases);
  }

  // Debug method to check worker status
  static async debugWorkerStatus(): Promise<void> {
    if (!this.isInitialized) {
      console.log('❌ Queue service not initialized');
      return;
    }

    try {
      console.log('🔍 Worker Status Debug:');
      console.log('- Worker exists:', !!this.worker);
      console.log('- Worker is running:', this.worker?.isRunning());
      console.log('- Worker is paused:', this.worker?.isPaused());

      const waiting = await this.testQueue.getWaiting();
      const active = await this.testQueue.getActive();
      const completed = await this.testQueue.getCompleted();
      const failed = await this.testQueue.getFailed();

      console.log('🔍 Queue Status:');
      console.log(`- Waiting: ${waiting.length}`);
      console.log(`- Active: ${active.length}`);
      console.log(`- Completed: ${completed.length}`);
      console.log(`- Failed: ${failed.length}`);

      if (waiting.length > 0) {
        console.log('🔍 Waiting job IDs:', waiting.map(j => j.id));
      }
    } catch (error) {
      console.error('❌ Error checking worker status:', error);
    }
  }

  // CLIENT_ID tracking helper methods
  private static addRunningJobForClient(clientId: string, jobId: string): void {
    if (!this.runningJobsByClientId.has(clientId)) {
      this.runningJobsByClientId.set(clientId, new Set());
    }
    this.runningJobsByClientId.get(clientId)!.add(jobId);
  }

  private static removeRunningJobForClient(clientId: string, jobId: string): void {
    const clientJobs = this.runningJobsByClientId.get(clientId);
    if (clientJobs) {
      clientJobs.delete(jobId);
      if (clientJobs.size === 0) {
        this.runningJobsByClientId.delete(clientId);
      }
    }
  }

  private static addWaitingJobForClient(clientId: string, jobId: string): void {
    if (!this.waitingJobsByClientId.has(clientId)) {
      this.waitingJobsByClientId.set(clientId, new Set());
    }
    this.waitingJobsByClientId.get(clientId)!.add(jobId);
  }

  private static removeWaitingJobForClient(clientId: string, jobId: string): void {
    const clientJobs = this.waitingJobsByClientId.get(clientId);
    if (clientJobs) {
      clientJobs.delete(jobId);
      if (clientJobs.size === 0) {
        this.waitingJobsByClientId.delete(clientId);
      }
    }
  }

  // Get CLIENT_ID-specific queue statistics
  static async getClientQueueStats(clientId: string): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const runningForClient = this.runningJobsByClientId.get(clientId)?.size || 0;
    const waitingForClient = this.waitingJobsByClientId.get(clientId)?.size || 0;

    // Get global stats for comparison
    const globalStats = await this.getQueueStats();

    return {
      success: true,
      data: {
        // CLIENT_ID-specific stats
        client: {
          waiting: waitingForClient,
          active: runningForClient,
          clientId: clientId
        },
        // Global stats for reference
        global: globalStats.data
      },
      timestamp: new Date().toISOString()
    };
  }

  // Graceful shutdown
  static async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Shutting down Queue Service...');
      
      if (this.worker) {
        await this.worker.close();
      }
      
      if (this.testQueue) {
        await this.testQueue.close();
      }
      
      await connection.quit();
      
      console.log('✅ Queue Service shut down successfully');
      this.isInitialized = false;
    } catch (error) {
      console.error('Error during Queue Service shutdown:', error);
    }
  }
}
