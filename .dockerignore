# Dependency directories
node_modules/
jspm_packages/
bower_components/
web_modules/

# Build output
dist/
build/
coverage/
.nyc_output
.npm
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.next
out
.nuxt
.cache
.parcel-cache
.temp
.docusaurus
.serverless/
.fusebox/
.dynamodb/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.vitepress/
.vitepress/dist
.vitepress/cache

# Logs and runtime data
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
pids/
*.pid
*.seed
*.pid.lock

# TypeScript cache
*.tsbuildinfo

# Optional cache and state files
.eslintcache
.stylelintcache
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.yarn-integrity

# dotenv environment variable files
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Test and debug output
# test-results/
# tests/
test-redis.js

# VSCode and IDE files
.vscode/
.idea/
.DS_Store
