#!/usr/bin/env node

// Test script to verify Kubernetes connection
const { KubernetesService } = require('../dist/services/kubernetes-service');

async function testKubernetesConnection() {
  console.log('🧪 Testing Kubernetes connection...');
  
  try {
    // Initialize the Kubernetes service
    await KubernetesService.initialize();
    console.log('✅ Kubernetes client initialized successfully');
    
    console.log('🎉 Kubernetes connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Kubernetes connection test failed:', error.message);
    console.error('💡 Make sure:');
    console.error('   - Minikube is running: minikube status');
    console.error('   - kubectl context is set: kubectl config current-context');
    console.error('   - ZAP image is available: minikube image ls | grep zap');
    process.exit(1);
  }
}

// Run the test
testKubernetesConnection();
