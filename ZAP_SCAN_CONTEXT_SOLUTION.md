# ZAP Scan Context Enhancement - Solution

## Problem
The user noticed that ZAP JSON reports stored in the Google bucket didn't contain details about which URLs were scanned. The original implementation only fetched vulnerability alerts from `/JSON/core/view/alerts/` but didn't include scan context information.

## Root Cause
The original `fetchZapJsonReport()` method in `ZapService` only called:
```typescript
`${this.ZAP_HOST}/JSON/core/view/alerts/?apikey=${this.ZAP_API_KEY}`
```

This endpoint only returns vulnerability/alert information but doesn't include:
- Which URLs were scanned
- Which sites were accessed
- Which hosts were contacted
- Target URL information
- Scan statistics

## Solution
Enhanced the `ZapService` to fetch comprehensive scan context information by adding multiple ZAP API endpoints:

### 1. New Interface: `ZapScanContext`
```typescript
export interface ZapScanContext {
  sites: string[];           // Sites that were accessed
  urls: string[];            // URLs that were scanned
  hosts: string[];           // Hosts that were contacted
  targetUrl?: string;        // Primary target URL
  scanStartTime?: string;    // When scan started
  scanEndTime?: string;      // When scan ended
  spiderResults?: any;       // Spider scan results
  activeScanResults?: any;   // Active scan results
}
```

### 2. New Method: `fetchZapScanContext()`
This method calls multiple ZAP API endpoints to gather complete scan context:

- `/JSON/core/view/sites/` - Lists all sites accessed
- `/JSON/core/view/urls/` - Lists all URLs accessed  
- `/JSON/core/view/hosts/` - Lists all hosts contacted
- `/JSON/spider/view/results/` - Spider scan results
- `/JSON/ascan/view/scans/` - Active scan results

### 3. Enhanced Report Data
The `ZapReportData` interface now includes:
```typescript
export interface ZapReportData {
  html: string;
  json?: any;
  timestamp: string;
  testCaseId: string;
  clientId: string;
  vulnerabilities: ZapVulnerability[];
  summary: ZapSummary;
  scanContext: ZapScanContext;  // NEW: Complete scan context
}
```

### 4. Enhanced Summary Statistics
The summary now includes URL count from scan context:
```typescript
export interface ZapSummary {
  totalIssues: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  informational: number;
  scanDuration?: number;
  urlsScanned?: number;  // Now populated from scanContext.urls.length
}
```

### 5. Enhanced Database Storage
The report stored in the database now includes scan context:
```typescript
zapReport: {
  reportPath: reportPath,
  summary: reportData.summary,
  vulnerabilities: reportData.vulnerabilities.slice(0, 10),
  timestamp: reportData.timestamp,
  clientId: reportData.clientId,
  scanContext: {  // NEW: Scan context information
    sites: reportData.scanContext.sites,
    urls: reportData.scanContext.urls.slice(0, 50),
    hosts: reportData.scanContext.hosts,
    targetUrl: reportData.scanContext.targetUrl,
    urlsScanned: reportData.scanContext.urls.length
  }
}
```

## Benefits
1. **Complete Scan Visibility**: JSON reports now show exactly which URLs were scanned
2. **Target Information**: Clear indication of the primary target URL
3. **Scan Statistics**: Accurate count of URLs, sites, and hosts accessed
4. **Better Debugging**: Easier to understand scan scope and coverage
5. **Enhanced Reporting**: More comprehensive security reports for stakeholders

## Testing
Run the test script to verify the enhancement:
```bash
node test-zap-context.js
```

This will show:
- Sites that were scanned
- URLs that were accessed
- Hosts that were contacted
- Target URL information
- Scan statistics

## Files Modified
- `src/services/zap-service.ts` - Enhanced with scan context fetching
- `test-zap-context.js` - Test script to demonstrate the enhancement

## Result
The ZAP JSON reports stored in Google bucket now contain complete scan context information, answering the user's question about which URLs were scanned.
