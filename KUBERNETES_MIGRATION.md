# Kubernetes Migration Guide

This document explains the migration from Docker containers to Kubernetes Jobs for ZAP security scanning in the AgentQ WebSocket DAST service.

## Overview

The service has been upgraded to use Kubernetes Jobs instead of Docker containers for better scalability, resource management, and isolation in SaaS environments.

### Benefits of Kubernetes Jobs

1. **Better Resource Management**: CPU and memory limits/requests
2. **Automatic Cleanup**: TTL for completed jobs
3. **Health Checks**: Readiness and liveness probes
4. **Scalability**: No port allocation conflicts
5. **Isolation**: Better security and resource isolation
6. **Monitoring**: Native Kubernetes monitoring and logging
7. **Fault Tolerance**: Automatic restart policies

## Configuration

### Environment Variables

```bash
# Kubernetes Configuration
USE_KUBERNETES=true                    # Enable Kubernetes mode (default: true)
K8S_NAMESPACE=default                  # Kubernetes namespace (default: default)
ZAP_IMAGE=zaproxy/zap-stable:latest   # ZAP Docker image (default: zaproxy/zap-stable:latest)
ZAP_API_KEY=AgentqSuperAI             # ZAP API key (default: AgentqSuperAI)

# Legacy Docker Configuration (when USE_KUBERNETES=false)
ZAP_PORT_RANGE_START=9001             # Start of port range for Docker containers
ZAP_PORT_RANGE_END=10000              # End of port range for Docker containers
```

### Kubernetes Setup

1. **Install Dependencies**:
   ```bash
   npm install @kubernetes/client-node
   ```

2. **Configure Kubernetes Access**:
   - **Production**: Uses in-cluster configuration automatically
   - **Development**: Uses local kubeconfig file

3. **Required Permissions**:
   The service account needs permissions to:
   - Create/Delete Jobs in the specified namespace
   - Create/Delete Services in the specified namespace
   - List/Watch Jobs and Pods

## Architecture

### Kubernetes Mode (Default)

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WebSocket     │    │  Kubernetes      │    │   ZAP Job       │
│   Service       │───▶│  Service         │───▶│   + Service     │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Legacy Docker Mode

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   WebSocket     │    │  Docker          │    │   ZAP           │
│   Service       │───▶│  Engine          │───▶│   Container     │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Migration Process

### Automatic Migration

The service automatically detects the environment and uses the appropriate mode:

1. **Kubernetes Mode** (default): When `USE_KUBERNETES=true` or not set
2. **Docker Mode**: When `USE_KUBERNETES=false`

### Backward Compatibility

- All existing APIs remain unchanged
- The `ZapContainerInfo` interface is maintained for compatibility
- Kubernetes Jobs are mapped to container-like structures internally

## Resource Management

### Job Configuration

Each ZAP Job includes:

```yaml
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1"
```

### Cleanup Policies

- **TTL**: Jobs are automatically cleaned up 5 minutes after completion
- **Active Deadline**: Jobs are killed if they run longer than 1 hour
- **Periodic Cleanup**: Orphaned jobs are cleaned up every 5 minutes

## Monitoring and Debugging

### Kubernetes Commands

```bash
# List all ZAP jobs
kubectl get jobs -l app=zap-scan

# Get job details
kubectl describe job zap-scan-<CLIENT_ID>

# View job logs
kubectl logs job/zap-scan-<CLIENT_ID>

# List ZAP services
kubectl get services -l app=zap-scan

# Check pod status
kubectl get pods -l app=zap-scan
```

### Service Logs

The service provides detailed logging for:
- Job creation and deletion
- Resource cleanup
- Error handling
- Performance metrics

## Troubleshooting

### Common Issues

1. **Job Creation Fails**:
   - Check Kubernetes permissions
   - Verify namespace exists
   - Check resource quotas

2. **ZAP Not Ready**:
   - Check readiness probe logs
   - Verify ZAP image is accessible
   - Check resource limits

3. **Cleanup Issues**:
   - Check job TTL settings
   - Verify cleanup permissions
   - Monitor periodic cleanup logs

### Fallback to Docker

If Kubernetes is not available, set:
```bash
USE_KUBERNETES=false
```

The service will automatically fall back to Docker container mode.

## Performance Considerations

### Kubernetes Advantages

- **No Port Conflicts**: Each job gets its own service
- **Resource Isolation**: Better CPU/memory management
- **Horizontal Scaling**: Can run hundreds of concurrent jobs
- **Automatic Cleanup**: No manual container management

### Resource Planning

- **Memory**: 1-2GB per ZAP job
- **CPU**: 0.5-1 CPU core per job
- **Storage**: Minimal (logs and temporary files)
- **Network**: ClusterIP services for internal communication

## Security

### Pod Security

- Runs as non-root user (UID 1000)
- No privilege escalation
- Minimal capabilities
- Read-only root filesystem where possible

### Network Security

- ClusterIP services (internal only)
- No external exposure by default
- API key authentication for ZAP

## Migration Checklist

- [ ] Install Kubernetes client dependency
- [ ] Configure environment variables
- [ ] Set up Kubernetes permissions
- [ ] Test job creation and cleanup
- [ ] Monitor resource usage
- [ ] Update monitoring and alerting
- [ ] Document operational procedures

## Support

For issues or questions about the Kubernetes migration:

1. Check service logs for detailed error messages
2. Verify Kubernetes cluster health
3. Test with `USE_KUBERNETES=false` for comparison
4. Review resource quotas and limits
